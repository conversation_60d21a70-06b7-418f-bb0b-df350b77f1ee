<template>
  <div class="py-2 bg-white relative overflow-hidden">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <div class="mx-3">
      <auto-table
        :headers="tableHeaders"
        :data="deposits"
        :loading="isLoading"
        :has-actions="true"
        :get-actions="getRowActions"
        :total-items="total"
        :items-per-page="limit"
        :current-page-prop="offset"
        :server-side-pagination="true"
        :pagination="total > limit"
        :show-items-count="true"
        :items-per-page-options="[10, 25, 50, 100]"
        @page-change="gotToPage"
        @items-per-page-change="handleLimitChange"
      >
        <!-- Transaction Code Column -->
        <template #trxn_code="{ item }">
          <span
            @click="copyToClipboard(item.trxn_code)"
            title="Click to copy"
            class="cursor-pointer hover:text-blue-600"
          >
            {{ item.trxn_code }}
          </span>
        </template>

      <!-- Customer Column -->
      <template #trxn_sender="{ item }">
        <div>
          <span>{{ item.trxn_sender || 'N/A' }}</span>
        </div>
      </template>

      <!-- Mobile Column -->
      <template #trxn_msisdn="{ item }">
        <div>
          <span 
            class="badge mobile-badge"
            @click="copyToClipboard(formatPhoneNumber(item.trxn_msisdn))"
            title="Click to copy"
          >
            {{ formatPhoneNumber(item.trxn_msisdn) }}
          </span>
        </div>
      </template>

      <!-- Amount Column -->
      <template #trxn_amount="{ item }">
        <div class="flex-col items-start">
          <span 
            class="badge amount-badge"
            :class="getAmountClass(parseFloat(item.trxn_amount))"
            @click="copyToClipboard('KES. ' + parseFloat(item.trxn_amount).toFixed(2))"
            title="Click to copy"
          >
            KES. {{ parseFloat(item.trxn_amount).toFixed(2) }}
          </span>
        </div>
      </template>

      <!-- Account Column -->
      <template #trxn_account="{ item }">
        <div class="badge-container">
          <span 
            class="badge account-badge"
            @click="copyToClipboard(item.trxn_account)"
            title="Click to copy"
          >
            {{ item.trxn_account }}
          </span>
        </div>
      </template>

      <!-- Transaction Type Column -->
      <template #trxn_repayment_type="{ item }">
        <div >
          <span>
            {{ (item.trxn_repayment_type) }}
          </span>
        </div>
      </template>

      <!-- Date Column -->
      <template #created_at="{ item }">
        <div class="badge-container">
          <span 
            class="badge date-badge"
            @click="copyToClipboard(moment(item.created_at).format('llll'))"
            title="Click to copy"
          >
            {{ moment(item.created_at).format('llll') }}
          </span>
        </div>
        </template>

        <!-- Actions Column -->
        <template #actions="{ item }">
          <div class="relative z-50">
            <action-dropdown
              button-text="Actions"
              :show-text="false"
              button-class="z-50"
              menu-class="z-50 origin-top-right"
              :menu-width="48"
            >
              <action-item
                text="View Details"
                color="blue"
                @click="viewDepositDetails(item)"
              >
                <template #icon>
                  <svg class="mr-3 h-5 w-5 text-blue-500 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </template>
              </action-item>
            </action-dropdown>
          </div>
        </template>
      </auto-table>

      <!-- View Details Modal -->
      <json-viewer-modal
        :show="showDetailsModal"
        :title="`Deposit Details - ${selectedDeposit?.trxn_code || 'N/A'}`"
        :json-data="selectedDeposit"
        @close="showDetailsModal = false"
      />
    </div>
  </div>
</template>

<script>
import moment from "moment";
import {mapActions} from "vuex";
import {endOfMonth, endOfYear, startOfMonth, startOfYear, subMonths} from "date-fns";
import VueDatePicker from "@vuepic/vue-datepicker";
import { AutoTable, CustomLoading, ActionDropdown, ActionItem, JsonViewerModal } from '@/components/common';

export default {
  components: {
    VueDatePicker,
    AutoTable,
    CustomLoading,
    ActionDropdown,
    ActionItem,
    JsonViewerModal
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      total: 0,
      limit: 10,
      offset: 1,
      showDropdown: [],
      viewModelOpen: false,
      deposits: [],
      phone: '',
      deposit: [],
      showDetailsModal: false,
      selectedDeposit: null,
      tableHeaders: [
        { key: 'trxn_code', label: 'Trx Code', align: 'center' },
        { key: 'trxn_sender', label: 'Sender', align: 'center' },
        { key: 'trxn_msisdn', label: 'Mobile', align: 'center' },
        { key: 'trxn_account', label: 'Account', align: 'center' },
        { key: 'trxn_amount', label: 'Amount', align: 'center' },
        { key: 'trxn_repayment_type', label: 'Trx Type', align: 'center' },
        { key: 'created_at', label: 'Date', align: 'left' }
      ],
      moreParams: {
        mpesa_code: '',
        mobile_number: '',
        start: '',
        end: '',
        page: '',
        limit: "10",
        timestamp: 'timestamp',
        skip_cache: '',
        paybill_number: '',
        account_number: '',
        name: '',
        amount: ''
      },
      date: null,
      presetRanges: [
        {label: 'Today', range: [new Date(), new Date()]},
        {label: 'This month', range: [startOfMonth(new Date()), endOfMonth(new Date())]},
        {
          label: 'Last month',
          range: [startOfMonth(subMonths(new Date(), 1)), endOfMonth(subMonths(new Date(), 1))],
        },
        {label: 'This year', range: [startOfYear(new Date()), endOfYear(new Date())]},
        {
          label: 'This year (slot)',
          range: [startOfYear(new Date()), endOfYear(new Date())],
          slot: 'yearly',
        },
      ],
    }
  },

  methods: {
    ...mapActions(["getDeposits", "repostDeposit"]),

    // Get row actions for AutoTable
    getRowActions(item) {
      return [
        {
          label: 'View Details',
          action: () => this.viewDepositDetails(item),
          icon: 'fas fa-eye'
        }
      ];
    },

    // Handle items per page change
    handleLimitChange(newLimit) {
      this.limit = newLimit;
      this.moreParams.limit = newLimit.toString();
      this.offset = 1;
      this.moreParams.page = '1';
      this.setDeposits(this.phone);
    },

    viewDepositDetails(item) {
      this.selectedDeposit = item;
      this.showDetailsModal = true;
    },

    formatPhoneNumber(phone) {
      if (!phone) return 'N/A';
      return phone.startsWith('+') ? phone : `+${phone}`;
    },

    getTransactionTypeClass(type) {
      if (!type) return 'type-default';
      
      const typeMap = {
        'OD': 'type-overdraft',
        'Overdraft': 'type-overdraft',
        'Payment': 'type-payment',
        'Deposit': 'type-deposit',
        'Refund': 'type-refund'
      };
      
      for (const [key, value] of Object.entries(typeMap)) {
        if (type.includes(key)) return value;
      }
      
      return 'type-default';
    },

    getTransactionTypeLabel(type) {
      if (!type) return 'Unknown';
      return type.length > 20 ? type.substring(0, 18) + '...' : type;
    },

    async selectDate() {
      this.isLoading = true;
      this.moreParams.start = this.formatDate(this.date[0]);
      this.moreParams.end = this.formatDate(this.date[1]);
      this.moreParams.timestamp = Date.now();
      await this.setDeposits(this.phone);
    },

    formatDate(date) {
      const d = new Date(date);
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      const year = d.getFullYear();
      
      return [year, month, day].join('-');
    },

    // Pagination
    gotToPage(page) {
      this.moreParams.page = page;
      this.offset = page;
      this.setDeposits(this.phone);
    },

    async setDeposits(num) {
      if (!num) return;
      
      this.isLoading = true;
      this.moreParams.mobile_number = num;
      this.phone = num;

      try {
        const params = new URLSearchParams();
        
        for (const key in this.moreParams) {
          if (this.moreParams.hasOwnProperty(key)) {
            params.append(key, this.moreParams[key]);
          }
        }

        const queryString = params.toString();
        const response = await this.getDeposits(queryString);
        
        if (response.status === 200) {
          this.deposits = response.message.result;
          this.total = parseInt(response.message.record_count);
          this.showDropdown = Array(this.deposits.length).fill(false);
        }
      } catch (error) {
        console.error("Error fetching deposits:", error);
      } finally {
        this.isLoading = false;
      }
    },

    copyToClipboard(text) {
      navigator.clipboard.writeText(text)
        .then(() => {
          // You could add a toast notification here if you have one
          console.log('Copied to clipboard:', text);
        })
        .catch(err => {
          console.error('Failed to copy text: ', err);
        });
    },

    getAmountClass(amount) {
      if (amount <= 100) return 'amount-low';
      if (amount <= 250) return 'amount-medium-low';
      if (amount <= 500) return 'amount-medium';
      if (amount <= 1000) return 'amount-medium-high';
      if (amount <= 5000) return 'amount-high';
      return 'amount-very-high';
    },

    // Format currency with commas and put negative figures in brackets (standardized from master)
    formatCurrency(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0.00';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },

    // Format numbers with commas (no decimal places for counts) and put negative figures in brackets
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '0';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },
  }
}
</script>

<style scoped>
/* Badge container to ensure consistent alignment */
.badge-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 4px;
}

/* Base badge styles */
.transaction-badge,
.customer-badge,
.mobile-badge,
.amount-badge,
.paybill-badge,
.account-badge,
.transaction-type-badge,
.date-badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  letter-spacing: 0.3px;
}

.badge-container .badge-container {
  padding: 0;
}

/* Transaction badge */
.transaction-badge {
  background-color: #e9ecef;
  color: #495057;
  min-width: 120px;
  font-family: monospace;
}

/* Customer badge */
.customer-badge {
  background-color: #f8f9fa;
  color: #212529;
  border: 1px solid #dee2e6;
  min-width: 100px;
}

/* Mobile badge */
.mobile-badge {
  background-color: #e9ecef;
  color: #495057;
  font-family: monospace;
  min-width: 120px;
}

/* Amount badge */
.amount-badge {
  background-color: #e3f2fd;
  color: #0d47a1;
  font-weight: 700;
  min-width: 120px;
  margin-bottom: 4px;
}

/* Paybill badge */
.paybill-badge {
  background-color: #f1f3f5;
  color: #495057;
  font-size: 0.7rem;
  min-width: 100px;
}

/* Account badge */
.account-badge {
  background-color: #e8f5e9;
  color: #1b5e20;
  font-family: monospace;
  min-width: 120px;
}

/* Transaction type badges */
.transaction-type-badge {
  min-width: 140px;
  color: white;
}

.type-payment {
  background-color: #2ecc71; /* Green */
}

.type-deposit {
  background-color: #3498db; /* Blue */
}

.type-overdraft {
  background-color: #f39c12; /* Orange */
}

.type-refund {
  background-color: #9b59b6; /* Purple */
}

.type-default {
  background-color: #95a5a6; /* Gray */
}

/* Date badge */
.date-badge {
  background-color: #f1f3f5;
  color: #495057;
  font-size: 0.7rem;
  min-width: 180px;
}

/* Hover effects for all badges */
.transaction-badge:hover,
.customer-badge:hover,
.mobile-badge:hover,
.amount-badge:hover,
.paybill-badge:hover,
.account-badge:hover,
.transaction-type-badge:hover,
.date-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  filter: brightness(105%);
}
</style>
