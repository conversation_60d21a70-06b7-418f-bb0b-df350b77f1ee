<template>
  <div class="py-2 bg-white relative overflow-hidden">
    <custom-loading :active="isLoading" :is-full-page="true" color="red" :blur="3" />

    <div class="block py-4 bg-white overflow-x-auto">
      <!-- Filters - Redesigned to match virtual_bets_table -->
      <div class="px-2 pb-2 flex flex-wrap gap-2 w-full">
        <!-- Ref ID -->
        <div class="w-48">
          <label class="block text-xs font-bold text-gray-700">Ref ID</label>
          <input type="text" @keyup.enter="applyFilters()"
                 class="block w-full px-2 py-1 text-xs text-gray-700 border rounded-md shadow-sm"
                 v-model="moreParams.client_unique_id">
        </div>

        <!-- Amount -->
        <div class="w-48">
          <label class="block text-xs font-bold text-gray-700">Amount</label>
          <input type="text" @keyup.enter="applyFilters()"
                 class="block w-full px-2 py-1 text-xs text-gray-700 border rounded-md shadow-sm"
                 v-model="moreParams.withdraw_amount">
        </div>

        <!-- Apply Button -->
        <div class="flex items-end">
          <button @click="applyFilters()"
                  class="px-3 py-1 bg-primary text-white text-xs rounded-md">
            Apply
          </button>
        </div>

        <!-- Reset Button -->
        <div class="flex items-end">
          <button @click="resetFilters()"
                  class="px-3 py-1 bg-gray-200 text-gray-700 text-xs rounded-md">
            Reset
          </button>
        </div>
      </div>

      <!-- Replace the table with AutoTable component -->
      <div
          v-if="withdrawals.length > 0" class="mx-3">
        <auto-table
          :headers="tableHeaders"
          :data="withdrawals"
          :has-actions="true"
          :get-actions="getRowActions"
          :total-items="total"
          :items-per-page="limit"
          :current-page-prop="offset"
          :server-side-pagination="true"
          :pagination="total > limit"
          :show-items-count="true"
          :items-per-page-options="[10, 25, 50, 100]"
          @page-change="gotToPage"
          @items-per-page-change="handleLimitChange"
        >
        <!-- Receipt Number Column -->
        <template #receipt_number="{ item }">
          <div class="receipt-badge">{{ item.receipt_number || 'N/A' }}</div>
        </template>

        <!-- Amount Column -->
        <template #withdraw_amount="{ item }">
          <div class="amount-badge">{{ item.currency }}. {{ formatNumber(item.withdraw_amount) }}</div>
          <br>
          <div class="charges-badge">Charges: {{ formatNumber(item.charges) }}</div>
        </template>

        <!-- Mobile Column -->
        <template #msisdn="{ item }">
          <div class="mobile-badge">{{ item.msisdn }}</div>
        </template>

        <!-- Recipient Column -->
        <template #receiver_name="{ item }">
          <div class="recipient-name-badge">{{ item.receiver_name || 'N/A' }}</div>
        </template>

        <!-- Unique/Ref ID Column -->
        <template #client_unique_id="{ item }">
          <!-- <div class="unique-id-badge">{{ item.client_unique_id || 'N/A' }}</div> -->
          <div class="reference-id-badge">{{ item.reference_id || 'N/A' }}</div>
        </template>

        <!-- Date Column -->
        <template #created_at="{ item }">
          <div class="date-badge">{{ moment(item.created_at).format('lll') }}</div>
        </template>

        <!-- Status Column -->
        <template #status="{ item }">
          <div class="status-badge" :class="getStatusClass(item)">
            {{ getStatusText(item) }}
          </div>
        </template>

        <!-- Actions Column -->
        <template #actions="{ item }">
          <div class="relative z-50">
            <action-dropdown 
              v-if="!((parseInt(item.result_code) === 0) && (parseInt(item.response_code) === 0)) && !(parseInt(item.result_code) === 0)"
              button-text="Actions" 
              :show-text="false"
              button-class="z-50"
              menu-class="z-50 origin-top-right"
              :menu-width="48"
            >
              <action-item
                text="Re-Process"
                color="indigo"
                @click="reprocessWithdrawal(item)"
              >
                <template #icon>
                  <svg class="mr-3 h-5 w-5 text-indigo-500 group-hover:text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                </template>
              </action-item>
              
              <action-item
                text="Repost to BC"
                color="green"
                @click="repostToBC(item)"
              >
                <template #icon>
                  <svg class="mr-3 h-5 w-5 text-green-500 group-hover:text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </template>
              </action-item>
              
              <action-item
                text="View Details"
                color="blue"
                @click="viewWithdrawalDetails(item)"
              >
                <template #icon>
                  <svg class="mr-3 h-5 w-5 text-blue-500 group-hover:text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                </template>
              </action-item>

            </action-dropdown>
          </div>
        </template>
        </auto-table>
      </div>

      <div v-else class="py-8 text-center text-gray-500">
        No withdrawal data available
      </div>

      <!-- View Details Modal -->
      <json-viewer-modal
        :show="showDetailsModal"
        :title="`Withdrawal Details - ${selectedWithdrawal?.reference_id || 'N/A'}`"
        :json-data="selectedWithdrawal"
        @close="showDetailsModal = false"
      />

    </div>
  </div>
</template>

<script>
import moment from "moment";
import { mapActions } from "vuex";
import { AutoTable, CustomLoading, ActionDropdown, ActionItem, JsonViewerModal } from '@/components/common';

export default {
  components: {
    AutoTable,
    CustomLoading,
    ActionDropdown,
    ActionItem,
    JsonViewerModal
  },
  data() {
    return {
      moment: moment,
      isLoading: false,
      fullPage: true,
      total: 0,
      limit: 10,
      offset: 1,
      showDropdown: [],
      viewModelOpen: false,
      withdrawal: null,
      showDetailsModal: false,
      selectedWithdrawal: null,
      trx_comp_date: "",
      org_util_bal: "",

      msisdn: '',
      moreParams: {
        status: "",
        mobile_number: "",
        start: "",
        end: "",
        page: "1",
        timestamp: Date.now().toString(),
        skip_cache: "",
        limit: "10",
        withdraw_desc: "",
        client_unique_id: "",
        withdraw_amount: "",
        receipt_number: "",
      },

      // Initialize with empty array to prevent null reference errors
      withdrawals: [],
      tableHeaders: [
        { key: 'client_unique_id', label: 'Unique - Ref Id', align: 'center' },
        { key: 'msisdn', label: 'Mobile', align: 'center' },
        { key: 'receipt_number', label: 'Receipt No', align: 'center' },
        { key: 'withdraw_amount', label: 'Amount', align: 'center' },
        { key: 'receiver_name', label: 'Recipient', align: 'center' },
        { key: 'status', label: 'Status', align: 'center' },
        { key: 'created_at', label: 'Date', align: 'center' },
      ],
    }
  },
  mounted() {
    // Initialize dropdown array with empty array to prevent null reference
    this.showDropdown = [];
  },
  methods: {
    ...mapActions(["getWithdrawals", "toggleSideMenu", "reprocessWithdrawal", "repostWithdrawalToBC"]),

    // Get row actions for AutoTable
    getRowActions(item) {
      const actions = [
        {
          label: 'View Details',
          action: () => this.viewWithdrawalDetails(item),
          icon: 'fas fa-eye'
        }
      ];

      if (!((parseInt(item.result_code) === 0) && (parseInt(item.response_code) === 0)) && !(parseInt(item.result_code) === 0)) {
        actions.push(
          {
            label: 'Re-Process',
            action: () => this.reprocessWithdrawal(item),
            icon: 'fas fa-redo'
          },
          {
            label: 'Repost to BC',
            action: () => this.repostToBC(item),
            icon: 'fas fa-bolt'
          }
        );
      }

      return actions;
    },

    // Handle items per page change
    handleLimitChange(newLimit) {
      this.limit = newLimit;
      this.moreParams.limit = newLimit.toString();
      this.offset = 1;
      this.moreParams.page = '1';
      this.setWithdrawals(this.phone);
    },

    // Pagination for main table
    gotToPage(page) {
      let vm = this
      vm.moreParams.page = page
      vm.offset = page
      vm.setWithdrawals(vm.msisdn)
    },

    toggleDetails(data) {
      this.viewModelOpen = true;
      this.withdrawal = data;
      if (this.withdrawal != null) {
        this.trx_comp_date = moment(this.withdrawal.transaction_completed_date).format('lll')
        this.org_util_bal = "KES. " + this.formatNumberWithCommas(this.withdrawal.org_utility_balance ?? 0)
      }
    },

    applyFilters() {
      this.moreParams.page = '1'
      this.offset = 1
      this.setWithdrawals(this.msisdn)
    },
    
    resetFilters() {
      this.moreParams = {
        status: "",
        mobile_number: this.msisdn, // Keep the mobile number
        start: "",
        end: "",
        page: "1",
        timestamp: Date.now().toString(),
        skip_cache: "",
        limit: "10",
        withdraw_desc: "",
        client_unique_id: "",
        withdraw_amount: "",
        receipt_number: "",
      };
      this.offset = 1;
      this.setWithdrawals(this.msisdn);
    },
    
    async setWithdrawals(mobile) {
      if (!mobile) return; // Guard against empty mobile
      
      this.isLoading = true;
      this.moreParams.mobile_number = mobile;
      this.msisdn = mobile;
      this.moreParams.timestamp = Date.now().toString(); // Update timestamp for fresh results
      
      try {
        const params = new URLSearchParams();
        for (const key in this.moreParams) {
          if (this.moreParams.hasOwnProperty(key)) {
            params.append(key, this.moreParams[key]);
          }
        }
        let queryString = params.toString();

        let response = await this.getWithdrawals(queryString);

        if (response && response.status === 200) {
          this.withdrawals = response.message.result || [];
          this.total = parseInt(response.message.record_count || 0);
          this.showDropdown = Array(this.withdrawals.length).fill(false);
        } else {
          this.withdrawals = [];
          this.total = 0;
          this.showDropdown = [];
        }
      } catch (error) {
        console.error("Error fetching withdrawals:", error);
        this.withdrawals = [];
        this.total = 0;
      } finally {
        this.isLoading = false;
      }
    },

    reprocessWithdrawal(item) {
      this.$swal.fire({
        title: 'Re-Process Withdrawal',
        text: "Are you sure you want to re-process this withdrawal?",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, re-process it!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async () => {
          // Implement reprocessWithdrawal action
          return await this.reprocessWithdrawal({
            withdrawal_id: item.id,
            timestamp: Date.now()
          });
        },
      }).then((result) => {
        if (result.value && result.value.status === 200) {
          this.$swal.fire('Success!', 'Withdrawal has been re-processed.', 'success');
          this.setWithdrawals(this.msisdn);
        } else {
          this.$swal.fire('Error!', result.value?.message || 'Failed to re-process withdrawal', 'error');
        }
      });
    },

    repostToBC(item) {
      this.$swal.fire({
        title: 'Repost to BC',
        text: "Are you sure you want to repost this withdrawal to BC?",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, repost it!',
        closeOnConfirm: false,
        showLoaderOnConfirm: true,
        preConfirm: async () => {
          // Implement repostWithdrawalToBC action
          return await this.repostWithdrawalToBC({
            withdrawal_id: item.id,
            timestamp: Date.now()
          });
        },
      }).then((result) => {
        if (result.value && result.value.status === 200) {
          this.$swal.fire('Success!', 'Withdrawal has been reposted to BC.', 'success');
          this.setWithdrawals(this.msisdn);
        } else {
          this.$swal.fire('Error!', result.value?.message || 'Failed to repost withdrawal to BC', 'error');
        }
      });
    },

    viewWithdrawalDetails(item) {
      this.selectedWithdrawal = item;
      this.showDetailsModal = true;
    },

    getStatusClass(item) {
      if (item.result_code === '0' && item.response_code === '0') {
        return 'status-success';
      } else if (item.result_code === '1') {
        return 'status-failed';
      } else {
        return 'status-pending';
      }
    },

    getStatusText(item) {
      if (item.result_code === '0' && item.response_code === '0') {
        return 'Success';
      } else if (item.result_code === '1') {
        return 'Failed';
      } else {
        return 'Pending';
      }
    },

    // Format currency with commas and put negative figures in brackets (standardized from master)
    formatCurrency(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0.00';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },

    // Format numbers with commas (no decimal places for counts) and put negative figures in brackets
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '0';
      }

      const num = parseFloat(value);

      if (isNaN(num)) {
        return '0';
      }

      const absNum = Math.abs(num);
      const formattedNum = absNum.toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      });

      // If negative, put in brackets
      if (num < 0) {
        return `(${formattedNum})`;
      }

      return formattedNum;
    },

    formatNumberWithCommas(number) {
      return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
  },
}
</script>

<style scoped>
/* Badge container to ensure consistent alignment */
.badge-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 4px;
}

.badge-container.flex-col {
  flex-direction: column;
  align-items: flex-start;
}

.badge-container.justify-center {
  justify-content: center;
}

/* Base badge styles */
.receipt-badge,
.amount-badge,
.charges-badge,
.mobile-badge,
.recipient-name-badge,
.recipient-details-badge,
.result-desc-badge,
.unique-id-badge,
.reference-id-badge,
.date-badge,
.status-badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  letter-spacing: 0.3px;
  margin: 2px 0;
}

/* Receipt badge */
.receipt-badge {
  background-color: #e9ecef;
  color: #495057;
  min-width: 120px;
  font-family: monospace;
}

/* Amount badge */
.amount-badge {
  background-color: #e3f2fd;
  color: #0d47a1;
  font-weight: 700;
  min-width: 120px;
}

/* Charges badge */
.charges-badge {
  background-color: #f1f3f5;
  color: #905c43;
  font-size: 0.7rem;
  min-width: 100px;
}

/* Mobile badge */
.mobile-badge {
  background-color: #e9ecef;
  color: #495057;
  font-family: monospace;
  min-width: 120px;
}

/* Recipient badges */
.recipient-name-badge {
  background-color: #e8f5e9;
  color: #1b5e20;
  min-width: 120px;
}

.recipient-details-badge {
  background-color: #f1f8e9;
  color: #33691e;
  font-size: 0.7rem;
  min-width: 120px;
}

.result-desc-badge {
  background-color: #f5f5f5;
  color: #616161;
  font-size: 0.7rem;
}

/* ID badges */
.unique-id-badge {
  background-color: #f3e5f5;
  color: #6a1b9a;
  font-family: monospace;
  min-width: 120px;
}

.reference-id-badge {
  background-color: #ede7f6;
  color: #4527a0;
  font-family: monospace;
  font-size: 0.7rem;
  min-width: 120px;
}

/* Date badge */
.date-badge {
  background-color: #e8eaf6;
  color: #283593;
  min-width: 120px;
  font-size: 0.7rem;
}

/* Status badges */
.status-badge {
  display: inline-block;
  padding: 6px 12px;
  min-width: 90px;
  border-radius: 20px;
  color: white;
  font-weight: 600;
  font-size: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  letter-spacing: 0.3px;
}

.status-success {
  background-color: #4caf50;
}

.status-pending {
  background-color: #ff9800;
}

.status-failed {
  background-color: #f44336;
}
</style>
